"use client";

import { <PERSON><PERSON> } from "@/src/presentation/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/presentation/components/ui/card";
import { Check, Star, Zap, Crown } from "lucide-react";

const pricingPlans = [
  {
    name: "Starter",
    price: "$29",
    period: "/month",
    description: "Perfect for individual investors getting started",
    icon: Star,
    popular: false,
    features: [
      "Up to 3 connected accounts",
      "Basic portfolio analytics",
      "Email notifications",
      "Mobile app access",
      "Standard support",
      "Basic AI insights"
    ],
    cta: "Start Free Trial",
    variant: "outline" as const
  },
  {
    name: "Professional",
    price: "$99",
    period: "/month",
    description: "Advanced features for serious traders",
    icon: Zap,
    popular: true,
    features: [
      "Unlimited connected accounts",
      "Advanced analytics & reporting",
      "Real-time notifications",
      "Copy trading access",
      "Priority support",
      "Advanced AI trading signals",
      "Risk management tools",
      "API access"
    ],
    cta: "Start Free Trial",
    variant: "default" as const
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "",
    description: "Tailored solutions for institutions",
    icon: Crown,
    popular: false,
    features: [
      "White-label solutions",
      "Custom integrations",
      "Dedicated account manager",
      "SLA guarantees",
      "Advanced compliance tools",
      "Custom AI model training",
      "On-premise deployment",
      "24/7 phone support"
    ],
    cta: "Contact Sales",
    variant: "outline" as const
  }
];

export function PricingSection() {
  return (
    <section id="pricing" className="py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Simple, Transparent Pricing
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Choose the plan that fits your trading needs. All plans include a 14-day free trial.
          </p>
        </div>

        {/* Pricing cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {pricingPlans.map((plan, index) => (
            <Card
              key={plan.name}
              className={`relative group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${
                plan.popular 
                  ? 'border-primary/50 bg-card shadow-lg scale-105' 
                  : 'border-border/50 bg-card/50 backdrop-blur-sm'
              }`}
              style={{
                animationDelay: `${index * 100}ms`
              }}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </div>
                </div>
              )}
              
              <CardHeader className="text-center pb-8">
                <div className={`w-12 h-12 rounded-lg ${
                  plan.popular ? 'bg-primary/20' : 'bg-muted/50'
                } flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <plan.icon className={`w-6 h-6 ${
                    plan.popular ? 'text-primary' : 'text-muted-foreground'
                  }`} />
                </div>
                <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                <div className="flex items-baseline justify-center gap-1 mt-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-muted-foreground">{plan.period}</span>
                </div>
                <CardDescription className="mt-2">{plan.description}</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  variant={plan.variant}
                  className="w-full"
                  size="lg"
                >
                  {plan.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional pricing info */}
        <div className="text-center space-y-4">
          <p className="text-muted-foreground">
            All plans include bank-level security, 99.9% uptime SLA, and access to our mobile app.
          </p>
          <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
            <span>✓ 14-day free trial</span>
            <span>✓ No setup fees</span>
            <span>✓ Cancel anytime</span>
            <span>✓ SOC 2 compliant</span>
          </div>
        </div>
      </div>
    </section>
  );
}
