"use client";

import { Card, CardContent } from "@/src/presentation/components/ui/card";
import {
  Users,
  Award,
  Globe,
  TrendingUp,
  Shield,
  Lightbulb,
  Heart
} from "lucide-react";

const values = [
  {
    icon: Shield,
    title: "Security First",
    description: "We prioritize the security of your assets with bank-level encryption and institutional-grade infrastructure."
  },
  {
    icon: Lightbulb,
    title: "Innovation",
    description: "Continuously pushing the boundaries of financial technology with cutting-edge AI and machine learning."
  },
  {
    icon: Heart,
    title: "Customer Success",
    description: "Your success is our success. We're committed to providing the tools and support you need to thrive."
  },
  {
    icon: Globe,
    title: "Accessibility",
    description: "Making professional-grade trading tools accessible to investors of all levels, from beginners to institutions."
  }
];

const stats = [
  {
    icon: Users,
    value: "10,000+",
    label: "Active Users"
  },
  {
    icon: TrendingUp,
    value: "$2.5B+",
    label: "Assets Under Management"
  },
  {
    icon: Globe,
    value: "50+",
    label: "Countries Served"
  },
  {
    icon: Award,
    value: "99.9%",
    label: "Platform Uptime"
  }
];

export function AboutSection() {
  return (
    <section id="about" className="py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              About Arthik.io
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Empowering the next generation of investors with AI-powered portfolio management
          </p>
        </div>

        {/* Mission statement */}
        <div className="mb-20">
          <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
            <CardContent className="p-8 md:p-12">
              <div className="max-w-4xl mx-auto text-center">
                <h3 className="text-2xl md:text-3xl font-bold mb-6">Our Mission</h3>
                <p className="text-lg text-muted-foreground leading-relaxed mb-8">
                  At Arthik.io, we believe that sophisticated investment tools shouldn't be limited to
                  Wall Street professionals. Our mission is to democratize access to institutional-grade
                  portfolio management technology, empowering individual investors and financial advisors
                  with the same advanced capabilities used by the world's largest investment firms.
                </p>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Through cutting-edge AI, seamless integrations, and intuitive design, we're building
                  the future of personal wealth management—one that's transparent, accessible, and
                  designed for your success.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Company stats */}
        <div className="mb-20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={stat.label}
                className="text-center group"
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-8 h-8 text-primary" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-foreground mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Values */}
        <div>
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Our Values</h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card
                key={value.title}
                className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-border/50 bg-card/50 backdrop-blur-sm"
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                      <value.icon className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                        {value.title}
                      </h4>
                      <p className="text-muted-foreground leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
